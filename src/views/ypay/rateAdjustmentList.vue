<template>
  <div class="app-container rwpage">
    <vxe-grid ref="xGrid" v-bind="gridOptions" @toolbar-button-click="toolbarButtonClickEvent">
      <template #ybProductQualificationInfo1="{ row }">
        <el-link v-show="row.ybAgreementSignUrl != null && row.ybAgreementSignUrl != ''
          " type="primary" @click="downloadFileEvent2(row)">
          协议签署
        </el-link>
        <span v-show="row.ybAgreementSignUrl == null || row.ybAgreementSignUrl == ''
          ">无</span>
      </template>
      <template v-slot:operate="{ row }">
        <div class="cell-btn-group"></div>
      </template>
      <template #ybProductQualificationInfo="{ row }">
        <el-link type="primary" @click="
          downloadFileEvent(
            row.ybProductQualificationInfo,
            'systemScreenshotUrl',
            '行业相关资质'
          )
          ">行业相关资质</el-link>
        <el-link style="margin-left: 15px" type="primary" @click="
          downloadFileEvent(
            row.ybProductQualificationInfo,
            'agreementPhotoUrl',
            '付款业务协议'
          )
          ">付款业务协议</el-link>
      </template>
      <template #ybMerchantNo="{ row }">
        <el-select size="small" v-model="queryForm.merchantId" placeholder="请选择" filterable clearable
          :loading="dataLoadingStates.merchants" @focus="handleMerchantSelectFocus">
          <el-option v-for="item in (merchantsList || [])" :label="item.merchantName" :key="'query-merchant-' + item.merchantId"
            :value="item.merchantId">
          </el-option>
        </el-select>
      </template>
      <template #ybApplicationStatus="{ row }">
        <el-select size="small" v-model="queryForm.ybApplicationStatus" placeholder="请选择" filterable clearable>
          <el-option v-for="item in ybApplicationStatusOptions" :label="item.label" :key="'status-' + item.value"
            :value="item.value">
          </el-option>
        </el-select>
      </template>

      <template #ybProductInfo="{ row }">
        <el-link type="primary" @click="showDetailEvent(row)">详情</el-link>
      </template>
      <template v-slot:buttons="{ row }">
        <div class="cell-btn-group">
          <el-button type="primary" size="mini" plain icon="el-icon-plus" @click="addNewEvent()"
            v-hasPermi="['system:productFeeModify:add']">新增变更</el-button>
        </div>
      </template>
    </vxe-grid>

    <vxe-modal v-model="showAddDetail" title="产品变更" :width="mobile ? '90%' : '800px'" height="610" show-footer show-zoom
      :loading="submitLoading" resize :destroy-on-close="true" class-name="cdetail">
      <div class="product-change-container">
        <!-- 商户选择 -->
        <div class="merchant-select-section">
          <div class="section-title">选择商户</div>
          <el-select size="small" v-model="currentRow.merchantId" placeholder="请选择商户" filterable clearable
            @change="selectMerchant" style="width: 100%;" :loading="dataLoadingStates.merchants">
            <el-option v-for="item in merchantsList" :label="item.merchantName" :key="'add-merchant-' + item.merchantId"
              :value="item.merchantId">
            </el-option>
          </el-select>

          <!-- 数据加载提示 -->
          <div v-if="isAnyDataLoading()" class="loading-tip">
            <i class="el-icon-loading"></i>
            <span>正在加载数据...</span>
          </div>

          <!-- 数据加载失败时的重试按钮 -->
          <div v-if="!isAnyDataLoading() && (!dataCacheStates.config || !dataCacheStates.merchants || !dataCacheStates.bankCodes)" class="retry-tip">
            <span>数据加载可能未完成</span>
            <el-button size="mini" type="text" @click="loadRequiredDataForAddForm()">重新加载</el-button>
          </div>

          <div class="merchant-info" v-if="currentRow.merchantId">
            <div class="info-item">
              <span class="info-label">商户简称：</span>
              <span class="info-value">{{ currentRow.merchantShortName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">商户编号：</span>
              <span class="info-value">{{ currentRow.merchantNo }}</span>
            </div>
          </div>
        </div>

        <!-- 产品选择 -->
        <div class="product-select-section" v-if="currentRow.merchantId">
          <div class="section-title">选择产品</div>
          <el-tabs v-model="activeTab" type="card" class="product-tabs-edit">
            <!-- VA账户选项卡 -->
            <el-tab-pane label="VA账户" name="vaAccount">

              <div class="product-grid">
                <div v-for="(product, index) in vaProducts" :key="'va-' + product.code" class="product-item"
                  :class="{ 'product-selected': product.selected, 'product-active': product.selected }"
                  @click="toggleProductSelection(product, $event)">
                  <div class="product-header">
                    <span class="product-name">{{ product.name }}</span>
                    <el-switch v-model="product.selected" @change="handleProductToggle(product, $event)" @click.stop></el-switch>
                  </div>

                  <!-- 费率设置 -->
                  <div class="rate-settings" v-if="product.selected" @click.stop>
                    <div class="rate-type">
                      <el-select v-model="product.rateType" size="small" placeholder="计费策略" @change="handleRateTypeChange(product)">
                        <el-option label="单笔百分比" value="SINGLE_PERCENT"></el-option>
                        <el-option label="单笔固定值" value="SINGLE_FIXED"></el-option>
                        <el-option label="单笔固定值+单笔百分比" value="FIXED_MIX_PERCENT"></el-option>
                      </el-select>
                    </div>

                    <div class="rate-values">
                      <div class="rate-value" v-if="product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT'">
                        <span class="rate-label">百分比：</span>
                        <el-input-number v-model="product.percentRate" :precision="2" :step="0.01" :min="0" size="small" @change="handleRateValueChange(product, 'percentRate', $event)"></el-input-number>
                        <span class="rate-unit">%</span>
                      </div>

                      <div class="rate-value" v-if="product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT'">
                        <span class="rate-label">固定值：</span>
                        <el-input-number v-model="product.fixedRate" :precision="2" :step="0.01" :min="0" size="small" @change="handleRateValueChange(product, 'fixedRate', $event)"></el-input-number>
                        <span class="rate-unit">元</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 支付产品选项卡 -->
            <el-tab-pane label="支付产品" name="paymentProduct">

              <!-- 面对面产品 -->
              <div class="product-category">
                <div class="category-header">
                  <div class="category-title">面对面</div>
                  <el-checkbox v-if="!isReadOnly" v-model="selectAllFace" @change="handleSelectAllFace">全部开通</el-checkbox>
                </div>

                <div class="product-grid">
                  <div v-for="(product, index) in faceProducts" :key="'face-' + product.code" class="product-item"
                    :class="{ 'product-selected': product.selected, 'product-active': product.selected }"
                    @click="toggleProductSelection(product, $event)">
                    <div class="product-header">
                      <span class="product-name">{{ product.name }}</span>
                      <el-switch v-model="product.selected" @change="handleProductToggle(product, $event)" @click.stop></el-switch>
                    </div>

                    <!-- 费率设置 -->
                    <div class="rate-settings" v-if="product.selected" @click.stop>
                      <div class="rate-type">
                        <el-select v-model="product.rateType" size="small" placeholder="计费策略" @change="handleRateTypeChange(product)">
                          <el-option label="单笔百分比" value="SINGLE_PERCENT"></el-option>
                          <el-option label="单笔固定值" value="SINGLE_FIXED"></el-option>
                          <el-option label="单笔固定值+单笔百分比" value="FIXED_MIX_PERCENT"></el-option>
                        </el-select>
                      </div>

                      <div class="rate-values">
                        <div class="rate-value" v-if="product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">百分比：</span>
                          <el-input-number v-model="product.percentRate" :precision="2" :step="0.01" :min="0.25" size="small" @change="handleRateValueChange(product, 'percentRate', $event)"></el-input-number>
                          <span class="rate-unit">%</span>
                        </div>

                        <div class="rate-value" v-if="product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">固定值：</span>
                          <el-input-number v-model="product.fixedRate" :precision="2" :step="0.01" :min="0" size="small" @change="handleRateValueChange(product, 'fixedRate', $event)"></el-input-number>
                          <span class="rate-unit">元</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 非面对面产品 -->
              <div class="product-category">
                <div class="category-header">
                  <div class="category-title">非面对面</div>
                  <el-checkbox v-if="!isReadOnly" v-model="selectAllNonFace" @change="handleSelectAllNonFace">全部开通</el-checkbox>
                </div>

                <div class="product-grid">
                  <div v-for="(product, index) in nonFaceProducts" :key="'nonface-' + product.code" class="product-item"
                    :class="{ 'product-selected': product.selected, 'product-active': product.selected }"
                    @click="toggleProductSelection(product, $event)">
                    <div class="product-header">
                      <span class="product-name">{{ product.name }}</span>
                      <el-switch v-model="product.selected" @change="handleProductToggle(product, $event)" @click.stop></el-switch>
                    </div>

                    <!-- 费率设置 -->
                    <div class="rate-settings" v-if="product.selected" @click.stop>
                      <div class="rate-type">
                        <el-select v-model="product.rateType" size="small" placeholder="计费策略" @change="handleRateTypeChange(product)">
                          <el-option label="单笔百分比" value="SINGLE_PERCENT"></el-option>
                          <el-option label="单笔固定值" value="SINGLE_FIXED"></el-option>
                          <el-option label="单笔固定值+单笔百分比" value="FIXED_MIX_PERCENT"></el-option>
                        </el-select>
                      </div>

                      <div class="rate-values">
                        <div class="rate-value" v-if="product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">百分比：</span>
                          <el-input-number v-model="product.percentRate" :precision="2" :step="0.01" :min="0.60" size="small" @change="handleRateValueChange(product, 'percentRate', $event)"></el-input-number>
                          <span class="rate-unit">%</span>
                        </div>

                        <div class="rate-value" v-if="product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">固定值：</span>
                          <el-input-number v-model="product.fixedRate" :precision="2" :step="0.01" :min="0" size="small" @change="handleRateValueChange(product, 'fixedRate', $event)"></el-input-number>
                          <span class="rate-unit">元</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <template #footer>
        <el-button @click="showAddDetail = false">关闭</el-button>
        <el-button type="primary" @click="saveClickEvent">确定</el-button>
      </template>
    </vxe-modal>

    <vxe-modal v-model="showInfoDetail" :title="'详情'" :width="mobile ? '90%' : '800px'" height="610" show-footer show-zoom
      :loading="submitLoading" resize :destroy-on-close="true" class-name="cdetail">
      <div class="product-change-container">
        <!-- 商户信息 -->
        <div class="merchant-select-section">
          <div class="section-title">商户信息</div>

          <div class="merchant-info">
            <div class="info-item">
              <span class="info-label">商户名称：</span>
              <span class="info-value">{{ currentRow.merchantName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">商户编号：</span>
              <span class="info-value">{{ currentRow.merchantNo }}</span>
            </div>
          </div>
        </div>
        <!-- 产品选择 -->
        <div class="product-select-section">
          <div class="section-title">产品信息</div>
          <el-tabs v-model="activeTab" type="card" class="product-tabs">
            <!-- VA账户选项卡 -->
            <el-tab-pane label="VA账户" name="vaAccount">
              <div class="category-header">
                <div class="category-title">VA账户</div>
                <el-checkbox v-if="!isReadOnly" v-model="selectAllVA" @change="handleSelectAllVA">全部开通</el-checkbox>
              </div>
              <div class="product-grid">
                <div v-for="(product, index) in vaProducts" :key="'detail-va-' + product.code" class="product-item"
                  :class="{ 'product-selected': product.selected }">
                  <div class="product-header">
                    <span class="product-name">{{ product.name }}</span>
                    <el-switch v-model="product.selected" disabled></el-switch>
                  </div>

                  <!-- 费率设置 -->
                  <div class="rate-settings" v-if="product.selected">
                    <div class="rate-type">
                      <el-select v-model="product.rateType" size="small" placeholder="计费策略" disabled>
                        <el-option label="单笔百分比" value="SINGLE_PERCENT"></el-option>
                        <el-option label="单笔固定值" value="SINGLE_FIXED"></el-option>
                        <el-option label="单笔固定值+单笔百分比" value="FIXED_MIX_PERCENT"></el-option>
                      </el-select>
                    </div>

                    <div class="rate-values">
                      <div class="rate-value" v-if="product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT'">
                        <span class="rate-label">百分比：</span>
                        <el-input-number v-model="product.percentRate" :precision="2" :step="0.01" :min="0" size="small" disabled></el-input-number>
                        <span class="rate-unit">%</span>
                      </div>

                      <div class="rate-value" v-if="product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT'">
                        <span class="rate-label">固定值：</span>
                        <el-input-number v-model="product.fixedRate" :precision="2" :step="0.01" :min="0" size="small" disabled></el-input-number>
                        <span class="rate-unit">元</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 支付产品选项卡 -->
            <el-tab-pane label="支付产品" name="paymentProduct">
              <!-- 面对面产品 -->
              <div class="product-category">
                <div class="category-header">
                  <div class="category-title">面对面</div>
                </div>

                <div class="product-grid">
                  <div v-for="(product, index) in faceProducts" :key="'detail-face-' + product.code" class="product-item"
                    :class="{ 'product-selected': product.selected }">
                    <div class="product-header">
                      <span class="product-name">{{ product.name }}</span>
                      <el-switch v-model="product.selected" disabled></el-switch>
                    </div>

                    <!-- 费率设置 -->
                    <div class="rate-settings" v-if="product.selected">
                      <div class="rate-type">
                        <el-select v-model="product.rateType" size="small" placeholder="计费策略" disabled>
                          <el-option label="单笔百分比" value="SINGLE_PERCENT"></el-option>
                          <el-option label="单笔固定值" value="SINGLE_FIXED"></el-option>
                          <el-option label="单笔固定值+单笔百分比" value="FIXED_MIX_PERCENT"></el-option>
                        </el-select>
                      </div>

                      <div class="rate-values">
                        <div class="rate-value" v-if="product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">百分比：</span>
                          <el-input-number v-model="product.percentRate" :precision="2" :step="0.01" :min="0" size="small" disabled></el-input-number>
                          <span class="rate-unit">%</span>
                        </div>

                        <div class="rate-value" v-if="product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">固定值：</span>
                          <el-input-number v-model="product.fixedRate" :precision="2" :step="0.01" :min="0" size="small" disabled></el-input-number>
                          <span class="rate-unit">元</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 非面对面产品 -->
              <div class="product-category">
                <div class="category-header">
                  <div class="category-title">非面对面</div>
                </div>

                <div class="product-grid">
                  <div v-for="(product, index) in nonFaceProducts" :key="'detail-nonface-' + product.code" class="product-item"
                    :class="{ 'product-selected': product.selected }">
                    <div class="product-header">
                      <span class="product-name">{{ product.name }}</span>
                      <el-switch v-model="product.selected" disabled></el-switch>
                    </div>

                    <!-- 费率设置 -->
                    <div class="rate-settings" v-if="product.selected">
                      <div class="rate-type">
                        <el-select v-model="product.rateType" size="small" placeholder="计费策略" disabled>
                          <el-option label="单笔百分比" value="SINGLE_PERCENT"></el-option>
                          <el-option label="单笔固定值" value="SINGLE_FIXED"></el-option>
                          <el-option label="单笔固定值+单笔百分比" value="FIXED_MIX_PERCENT"></el-option>
                        </el-select>
                      </div>

                      <div class="rate-values">
                        <div class="rate-value" v-if="product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">百分比：</span>
                          <el-input-number v-model="product.percentRate" :precision="2" :step="0.01" :min="0" size="small" disabled></el-input-number>
                          <span class="rate-unit">%</span>
                        </div>

                        <div class="rate-value" v-if="product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT'">
                          <span class="rate-label">固定值：</span>
                          <el-input-number v-model="product.fixedRate" :precision="2" :step="0.01" :min="0" size="small" disabled></el-input-number>
                          <span class="rate-unit">元</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <template #footer>
        <el-button @click="showInfoDetail = false">关闭</el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import { getPagerLayouts, tableHeight, screenMedia } from "@/utils";
import { linkDownloadurl } from "@/api/index";
import Treeselect from "@riophae/vue-treeselect";
import { saveProductFeeModify } from "@/api/ypay/merchant";
import { listData } from "@/api/system/dict/data";
import { regionData } from "element-china-area-data"; //引入
const { isIpad, isMobile } = screenMedia()
export default {
  name: "RateAdjustmentList",
  components: { Treeselect },

  data() {
    return {
      isReadOnly: true,
      showInfoDetail: false,
      showAddDetail: false,
      merchantsList: [],
      yb_settlement_direction_type: [],
      yb_bank_account_type: [],
      bankCodeList: [],
      // 产品变更新增字段
      activeProductTab: "vaAccount", // 当前激活的产品Tab
      selectedProducts: [], // 已选择的产品列表

      // 产品变更相关数据
      activeTab: "vaAccount", // 当前激活的产品Tab
      productList: [], // 产品列表
      selectAllVA: false, // 全选VA账户
      selectAllFace: false, // 全选面对面
      selectAllNonFace: false, // 全选非面对面

      // VA账户产品
      vaProducts: [
        { name: "百信银行", code: "ENTERPRISE_RECHARGE_STANDARD_BANKACCOUNTPAY_HXBXB", selected: false },
        { name: "江苏苏商银行", code: "ENTERPRISE_RECHARGE_STANDARD_BANKACCOUNTPAY_SUNINGBANK", selected: false },
        { name: "蓝海银行", code: "ENTERPRISE_RECHARGE_STANDARD_BANKACCOUNTPAY_WHLHB", selected: false },
        { name: "新网银行", code: "ENTERPRISE_RECHARGE_STANDARD_BANKACCOUNTPAY_XWB", selected: false }
      ],

      // 面对面支付产品
      faceProducts: [
        { name: "商家扫码_微信_线下", code: "MERCHANT_SCAN_WECHAT_OFFLINE", selected: false },
        { name: "用户扫码_微信_线下", code: "USER_SCAN_WECHAT_OFFLINE", selected: false },
        { name: "静态台牌_微信_线下", code: "STATICQR_WECHAT_OFFLINE", selected: false },
        { name: "静态台牌_微信_借记卡", code: "STATICQR_WECHAT_DEBIT", selected: false },
        { name: "静态台牌_微信_贷记卡", code: "STATICQR_WECHAT_CREDIT", selected: false },
        { name: "用户扫码_支付宝_线下", code: "USER_SCAN_ALIPAY_OFFLINE", selected: false },
        { name: "商家扫码_支付宝_线下", code: "MERCHANT_SCAN_ALIPAY_OFFLINE", selected: false },
        { name: "静态台牌_支付宝_线下", code: "STATICQR_ALIPAY_OFFLINE", selected: false },
        { name: "商家扫码_银联_贷记卡", code: "MERCHANT_SCAN_UNIONPAY_CREDIT", selected: false },
        { name: "商家扫码_银联_借记卡", code: "MERCHANT_SCAN_UNIONPAY_DEBIT", selected: false },
        { name: "用户扫码_银联_贷记卡", code: "USER_SCAN_UNIONPAY_CREDIT", selected: false },
        { name: "用户扫码_银联_借记卡", code: "USER_SCAN_UNIONPAY_DEBIT", selected: false },
        { name: "静态台牌_银联_借记卡", code: "STATICQR_UNIONPAY_DEBIT", selected: false },
        { name: "静态台牌_银联_贷记卡", code: "STATICQR_UNIONPAY_CREDIT", selected: false },
        { name: "H5支付_微信_线下", code: "H5_PAY_M_WECHAT_OFFLINE", selected: false }
      ],

      // 非面对面支付产品
      nonFaceProducts: [
        { name: "商家扫码_微信_线上", code: "MERCHANT_SCAN_WECHAT_ONLINE", selected: false },
        { name: "H5支付_微信_线上", code: "H5_PAY_M_WECHAT_ONLINE", selected: false },
        { name: "SDK支付_微信支付_线上", code: "SDK_PAY_WECHAT_ONLINE", selected: false },
        { name: "公众号支付_微信_线上", code: "WECHAT_OFFIACCOUNT_WECHAT_ONLINE", selected: false },
        { name: "小程序支付_微信_线上", code: "MINI_PROGRAM_WECHAT_ONLINE", selected: false },
        { name: "用户扫码_微信_线上", code: "USER_SCAN_WECHAT_ONLINE", selected: false }
      ],

      selectIsloading: false,
      submitLoading: false,

      // 数据加载状态管理
      dataLoadingStates: {
        config: false,        // 配置数据加载状态
        merchants: false,     // 商户列表加载状态
        bankCodes: false      // 银行代码列表加载状态
      },

      // 数据缓存状态
      dataCacheStates: {
        config: false,        // 配置数据是否已缓存
        merchants: false,     // 商户列表是否已缓存
        bankCodes: false      // 银行代码列表是否已缓存
      },
      queryForm: {},
      currentRow: {},
      ybApplicationStatusOptions: [
        { value: "0", label: "审核中" },
        { value: "1", label: "已完成" },
        { value: "2", label: "已驳回" },
        { value: "3", label: "协议待签署" },
        { value: "4", label: "业务开通中" },
      ],
      gridOptions: {
        border: true,
        resizable: true,
        showHeaderOverflow: true,
        showOverflow: true,
        highlightHoverRow: true,
        keepSource: true,
        id: "purchase",
        //height: tableHeight(125),
        minHeight: tableHeight(125),
        rowId: "xid",
        size: "small",
        sortConfig: { trigger: "cell" },
        filterConfig: { remote: true },
        pagerConfig: {
          pageSize: 10,
          pageSizes: [10, 20, 50, 100, 200],
          layouts: getPagerLayouts(),
        },
        toolbarConfig: {
          refresh: true,
          import: false,
          export: false,
          print: false,
          zoom: true,
          custom: true,
          slots: { buttons: "buttons" },
        },
        formConfig: {
          titleWidth: 80,
          titleAlign: "right",
          items: [
            {
              field: "ybMerchantNo",
              title: "商户名称",
              span: isIpad ? 12 : isMobile ? 24 : 6,
              slots: { default: "ybMerchantNo" },
            },
            {
              field: "ybApplicationStatus",
              title: "申请状态",
              span: isIpad ? 12 : isMobile ? 24 : 6,
              slots: { default: "ybApplicationStatus" },
            },
            {
              field: "ybApplicationNo",
              title: "申请单号",
              span: isIpad ? 12 : isMobile ? 24 : 6,
              itemRender: {
                name: "$input",
                props: {
                  placeholder: "请输入",
                  filterable: true,
                  clearable: true,
                },
              },
            },
            {
              span: isIpad ? 12 : isMobile ? 24 : 6,
              align: "right",
              collapseNode: false,
              itemRender: {
                name: "$buttons",
                children: [
                  {
                    props: {
                      icon: "vxe-icon-search",
                      type: "submit",
                      content: "查询",
                      status: "primary",
                    },
                  },
                  {
                    props: { icon: "vxe-icon-refresh", content: "重置" },
                    events: { click: this.resetEvent },
                  },
                ],
              },
            },
          ],
        },
        proxyConfig: {
          seq: true, // 启用动态序号代理
          sort: true, // 启用排序代理
          filter: true, // 启用筛选代理
          form: true, // 启用表单代理
          props: { total: "total", result: "rows" },
          ajax: {
            query: ({ page, sort, filters, form }) => {
              const queryParams = Object.assign(
                { sort: sort.property, order: sort.order },
                form
              );
              filters.forEach(({ property, values }) => {
                queryParams[property] = values.join(",");
              });
              //分页
              queryParams.pageSize = page.pageSize;
              queryParams.pageNum = page.currentPage;
              queryParams.deptId = this.queryForm.deptId;
              queryParams.userId = this.queryForm.userId;
              queryParams.status = this.queryForm.status;
              queryParams.bankId = this.queryForm.bankId;

              // queryParams.ybMerchantNo = this.queryForm.ybMerchantNo ;
              queryParams.merchantId = this.queryForm.merchantId;
              queryParams.ybApplicationStatus =
                this.queryForm.ybApplicationStatus;
              queryParams.orderByColumn = "id";
              queryParams.isAsc = "desc";
              return this.getRequest(
                "/system/productFeeModify/list",
                queryParams
              );
            },
          },
        },

        columns: [
          { type: "seq", width: 50 },
          //产品费率调整列表：商户编码、流水号、申请单号、申请状态、审核意见、进度说明、协议签署、申请时间（create_time）
          /*{ field: 'merchantId', title: '所属商户', minWidth: 100 },*/
          { field: "ybMerchantNo", title: "商户编码", width: 100 },
          { field: "requestNo", title: "流水号", width: 210 },
          { field: "ybApplicationNo", title: "申请单号", width: 210 },
          {
            field: "ybApplicationStatus",
            title: "申请状态",
            width: 100,
            formatter({ cellValue }) {
              if (cellValue == "0") {
                return "审核中";
              }
              if (cellValue == "1") {
                return "已完成";
              }
              if (cellValue == "2") {
                return "已驳回";
              }
              if (cellValue == "3") {
                return "协议待签署";
              }
              if (cellValue == "4") {
                return "业务开通中";
              }
              return cellValue;
            },
          }, //0 审核中;1已完成;2已驳回;3协议待签署;4业务开通中
          { field: "ybAuditOpinion", title: "审核意见", minWidth: 150 },
          { field: "ybProgressDescription", title: "进度说明", minWidth: 150 },
          //    { field: 'ybProductQualificationInfo', title: '产品资质信息', width: 210 ,slots: { default: 'ybProductQualificationInfo'  }},
          {
            field: "ybAgreementSignUrl",
            title: "协议签署",
            width: 210,
            slots: { default: "ybProductQualificationInfo1" },
          },
          { field: "createTime", title: "申请时间", width: 150 },
          //  { field: 'ybFunctionServiceQualificationInfo', title: '功能服务配套资质信息', minWidth: 100 },

          /*          { field: 'ybSettlementAccountInfo', title: '结算账户信息', minWidth: 150 },
          { field: 'ybFunctionService', title: '功能服务', minWidth: 100 },*/
          {
            field: "ybProductInfo",
            title: "操作",
            width: 100,
            slots: { default: "ybProductInfo" },
            fixed: "right",
          },
          //  { field: 'ybAgreementSignUrl', title: '易宝协议签署地址', minWidth: 100 },
          //   { field: 'status', title: '状态：0停用，1启用', minWidth: 100 },
          // {"minWidth":110,"title":"操作",  slots: { default: 'operate'  },fixed:'right'},
        ],
      },
      formRules: {
        merchantId: [{ required: true, message: "不能为空" }],
        merchantName: [{ required: true, message: "不能为空" }],
        merchantNo: [{ required: true, message: "不能为空" }],

        contactName: [{ required: true, message: "不能为空" }],
        contactMobile: [{ required: true, message: "不能为空" }],
        contactEmail: [
          { required: true, message: "不能为空" },
          {
            validator({ itemValue }) {
              if (!validateEmail(itemValue)) {
                return new Error("邮箱填写不正确");
              }
              function validateEmail(email) {
                const emailPattern =
                  /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                return emailPattern.test(email);
              }
            },
          },
        ],
        contactLicenceNo: [{ required: true, message: "不能为空" }],

        legalLicenceType: [{ required: true, message: "不能为空" }],
        legalName: [{ required: true, message: "不能为空" }],
        legalLicenceNo: [{ required: true, message: "不能为空" }],
        legalLicenceFrontUrl: [{ required: true, message: "不能为空" }],
        legalLicenceBackUrl: [{ required: true, message: "不能为空" }],
        signType: [{ required: true, message: "不能为空" }],
        licenceNo: [{ required: true, message: "不能为空" }],
        licenceUrl: [{ required: true, message: "不能为空" }],
        //signName: [{ required: true, message: "不能为空" }],
        /* primaryIndustryCategory: [{ required: true, message: "不能为空" }],
         secondaryIndustryCategory: [{ required: true, message: "不能为空" }],*/
        industryCategory: [{ required: true, message: "不能为空" }],
        /*      province: [{ required: true, message: "不能为空" }],
              city: [{ required: true, message: "不能为空" }],*/
        district: [{ required: true, message: "不能为空" }],
        address: [{ required: true, message: "不能为空" }],

        T1: [{ required: true, message: "不能为空" }],
        settlementDirection: [{ required: true, message: "不能为空" }],
        bankCode: [{ required: true, message: "不能为空" }],
        bankAccountType: [{ required: true, message: "不能为空" }],
        bankCardNo: [{ required: true, message: "不能为空" }],

        merchantShortName: [{ required: true, message: "不能为空" }],
        paymentScene: [{ required: true, message: "不能为空" }],
      },
      formKhRules: {
        merchantNo: [{ required: true, message: "不能为空" }],
        merchantName: [{ required: true, message: "不能为空" }],
        openAccountType: [{ required: true, message: "不能为空" }],
        certificateNo: [{ required: true, message: "不能为空" }],
        magAddress: [{ required: true, message: "不能为空" }],
        businessScope: [{ required: true, message: "不能为空" }],
        contactName: [{ required: true, message: "不能为空" }],
        contactMobile: [{ required: true, message: "不能为空" }],
        contactCardType: [{ required: true, message: "不能为空" }],
        contactCardNo: [{ required: true, message: "不能为空" }],
        contactCardStartDate: [{ required: true, message: "不能为空" }],
        contactCardExpireDate: [{ required: true, message: "不能为空" }],
        contactCardImageFont: [{ required: true, message: "不能为空" }],
        contactCardImageBack: [{ required: true, message: "不能为空" }],
        legalName: [{ required: true, message: "不能为空" }],
        legalMobile: [{ required: true, message: "不能为空" }],
        // legalIdCard: [{ required: true, message: "不能为空" }],
        legalLicenceEffectiveDate: [{ required: true, message: "不能为空" }],
        legalLicenceExpirationDate: [{ required: true, message: "不能为空" }],

        legalCardImageFont: [{ required: true, message: "不能为空" }],
        legalCardImageBack: [{ required: true, message: "不能为空" }],
        benefitName: [{ required: true, message: "不能为空" }],
      },
      defRwValue: {
        merchantId: '',
        merchantNo: '',
        ybMerchantNo: '',
        merchantName: '',
        merchantShortName: '',
        paymentScene: ''
      },
    };
  },
  computed: {
    mobile() {
      return isMobile ? 24 : 12
    }
  },
  mounted() {
    // 移除初始化API请求，改为按需加载
    // 只在用户打开新增变更界面时才加载相关数据
  },
  methods: {
    // 事件触发
    handleChange(e) {
    },
    searchEvent() {
      const $grid = this.$refs.xGrid;
      $grid.commitProxy("query");
    },
    showDetailEvent(row) {
      // 参数验证
      if (!row) {
        console.error('showDetailEvent: row参数为空');
        this.$message.error('数据错误，请刷新页面重试');
        return;
      }

      // 重置产品选择状态
      this.initProductData();
      this.activeTab = "vaAccount";
      this.isReadOnly = true;

      // 处理基本信息
      let ybSettlementAccountInfo, ybProductQualificationInfo;
      try {
        ybSettlementAccountInfo = row.ybSettlementAccountInfo ? JSON.parse(row.ybSettlementAccountInfo) : {};
        ybProductQualificationInfo = row.ybProductQualificationInfo ? JSON.parse(row.ybProductQualificationInfo) : {};
      } catch (e) {
        console.error('JSON解析错误:', e);
        return;
      }

      // 先设置当前行数据
      this.currentRow = Object.assign({}, this.defRwValue, {
        merchantId: row.merchantId,
        merchantNo: row.ybMerchantNo || '',
        paymentScene: ybProductQualificationInfo.paymentScene
      });

      // 显示弹出框
      this.showInfoDetail = true;

      // 获取商户名称
      this.getRequest(`/system/RegisterMerchant/${row.merchantId}`)
        .then((resp) => {
          // 检查响应格式，可能是data字段或者直接是响应对象
          const merchantData = resp.data || resp;
          if (merchantData) {
            // 使用Vue.set或者对象属性赋值的方式确保响应式更新
            const newCurrentRow = {...this.currentRow, merchantName: merchantData.merchantName};
            this.currentRow = newCurrentRow;
            console.log('商户名称获取成功:', merchantData.merchantName);

            // 强制触发视图更新
            this.$forceUpdate();
          } else {
            console.error('商户数据格式不正确:', resp);
          }
        })
        .catch((err) => {
          console.error('获取商户数据失败:', err);
        });

      // 不需要获取银行信息

      // 处理产品信息
      if (row.ybProductInfo && row.ybProductInfo !== '') {
        try {
          const productInfoList = JSON.parse(row.ybProductInfo);

          // 处理VA账户产品
          this.vaProducts.forEach(product => {
            const matchedProduct = productInfoList.find(p => p.productCode === product.code);
            if (matchedProduct) {
              product.selected = true;
              product.rateType = matchedProduct.rateType || 'SINGLE_PERCENT';
              product.percentRate = matchedProduct.percentRate !== undefined ? Number(matchedProduct.percentRate) : 0.60;
              product.fixedRate = matchedProduct.fixedRate !== undefined ? Number(matchedProduct.fixedRate) : 0;

              // 保存原始值，用于开关切换时恢复
              product._originalValues = {
                rateType: product.rateType,
                percentRate: product.percentRate,
                fixedRate: product.fixedRate
              };
            }
          });

          // 处理面对面产品
          this.faceProducts.forEach(product => {
            const matchedProduct = productInfoList.find(p => p.productCode === product.code);
            if (matchedProduct) {
              product.selected = true;
              product.rateType = matchedProduct.rateType || 'FIXED_MIX_PERCENT';
              product.percentRate = matchedProduct.percentRate !== undefined ? Number(matchedProduct.percentRate) : 0.60;
              product.fixedRate = matchedProduct.fixedRate !== undefined ? Number(matchedProduct.fixedRate) : 0;

              // 保存原始值，用于开关切换时恢复
              product._originalValues = {
                rateType: product.rateType,
                percentRate: product.percentRate,
                fixedRate: product.fixedRate
              };
            }
          });

          // 处理非面对面产品
          this.nonFaceProducts.forEach(product => {
            const matchedProduct = productInfoList.find(p => p.productCode === product.code);
            if (matchedProduct) {
              product.selected = true;
              product.rateType = matchedProduct.rateType || 'FIXED_MIX_PERCENT';
              product.percentRate = matchedProduct.percentRate !== undefined ? Number(matchedProduct.percentRate) : 0.60;
              product.fixedRate = matchedProduct.fixedRate !== undefined ? Number(matchedProduct.fixedRate) : 0;

              // 保存原始值，用于开关切换时恢复
              product._originalValues = {
                rateType: product.rateType,
                percentRate: product.percentRate,
                fixedRate: product.fixedRate
              };
            }
          });

          // 更新全选状态
          this.updateSelectAllStatus();

        } catch (error) {
          console.error('Failed to parse product info:', error);
        }
      }

    },
    downloadFileEvent(obj1, type, name) {
      let obj = JSON.parse(obj1);
      if (obj) {
        let url = obj[type];
        linkDownloadurl(url, name);
      }
    },
    downloadFileEvent2(row) {
      linkDownloadurl(row.ybAgreementSignUrl, "协议签署");
    },
    toolbarButtonClickEvent(code) {
      if (code === "addInfo") {
        this.addNewEvent();
      }
    },
    resetEvent() {
      this.queryForm = {};
      const $grid = this.$refs.xGrid;
      $grid.$children[0].reset();
      $grid.commitProxy("reload");
    },

    // 按需加载新增变更界面所需的数据
    async loadRequiredDataForAddForm() {
      console.log('开始按需加载新增变更界面数据...');

      try {
        // 并行加载所有需要的数据
        const loadPromises = [];

        // 如果配置数据未缓存，则加载
        if (!this.dataCacheStates.config) {
          loadPromises.push(this.loadConfigData());
        }

        // 如果商户列表未缓存，则加载
        if (!this.dataCacheStates.merchants) {
          loadPromises.push(this.loadMerchantList());
        }

        // 如果银行代码列表未缓存，则加载
        if (!this.dataCacheStates.bankCodes) {
          loadPromises.push(this.loadBankCodeList());
        }

        // 等待所有数据加载完成
        if (loadPromises.length > 0) {
          await Promise.all(loadPromises);
          console.log('新增变更界面数据加载完成');
        } else {
          console.log('所有数据已缓存，无需重新加载');
        }

      } catch (error) {
        console.error('加载新增变更界面数据失败:', error);
        this.$message.error('加载数据失败，请重试');

        // 可以选择关闭弹窗或者让用户手动重试
        // this.showAddDetail = false;
      }
    },

    // 加载配置数据
    async loadConfigData() {
      if (this.dataLoadingStates.config) {
        return; // 如果正在加载，避免重复请求
      }

      this.dataLoadingStates.config = true;

      try {
        const [settlementResponse, bankAccountResponse] = await Promise.all([
          listData({ dictType: "yb_settlement_direction_type" }),
          listData({ dictType: "yb_bank_account_type" })
        ]);

        if (settlementResponse.code == 200) {
          this.yb_settlement_direction_type = settlementResponse.rows;
        }

        if (bankAccountResponse.code == 200) {
          this.yb_bank_account_type = bankAccountResponse.rows;
        }

        this.dataCacheStates.config = true;
        console.log('配置数据加载完成');

      } catch (error) {
        console.error('加载配置数据失败:', error);
        throw error;
      } finally {
        this.dataLoadingStates.config = false;
      }
    },

    // 保留原有的getConfig方法以兼容其他可能的调用
    getConfig() {
      return this.loadConfigData();
    },
    // 加载商户列表数据
    async loadMerchantList() {
      if (this.dataLoadingStates.merchants) {
        return; // 如果正在加载，避免重复请求
      }

      this.dataLoadingStates.merchants = true;

      try {
        const retModel = await this.getRequest("/system/RegisterMerchant/list", {
          ybApplicationStatus: 1,
          pageNum: 1,
          pageSize: 9999,
          orderByColumn: "id",
          isAsc: "desc"
        });

        if (retModel.code == 200) {
          this.merchantsList = retModel.rows;
          this.dataCacheStates.merchants = true;
          console.log('商户列表加载完成，共', retModel.rows.length, '条记录');
        }

      } catch (error) {
        console.error('加载商户列表失败:', error);
        throw error;
      } finally {
        this.dataLoadingStates.merchants = false;
      }
    },

    // 保留原有的getMerchantList方法以兼容其他可能的调用
    getMerchantList() {
      return this.loadMerchantList();
    },


    // 加载银行代码列表数据
    async loadBankCodeList() {
      if (this.dataLoadingStates.bankCodes) {
        return; // 如果正在加载，避免重复请求
      }

      this.dataLoadingStates.bankCodes = true;

      try {
        const retModel = await this.getRequest("/system/ybBank/list?pageSize=1000");

        if (retModel.code == 200) {
          this.bankCodeList = retModel.rows;
          this.dataCacheStates.bankCodes = true;
          console.log('银行代码列表加载完成，共', retModel.rows.length, '条记录');
        }

      } catch (error) {
        console.error('加载银行代码列表失败:', error);
        throw error;
      } finally {
        this.dataLoadingStates.bankCodes = false;
      }
    },

    // 保留原有的getBankCodeList方法以兼容其他可能的调用
    getBankCodeList() {
      return this.loadBankCodeList();
    },

    // 清除数据缓存（可选功能，用于强制刷新数据）
    clearDataCache() {
      this.dataCacheStates = {
        config: false,
        merchants: false,
        bankCodes: false
      };
      console.log('数据缓存已清除');
    },

    // 检查是否有数据正在加载
    isAnyDataLoading() {
      return Object.values(this.dataLoadingStates).some(loading => loading);
    },

    // 处理商户选择框焦点事件，按需加载商户数据
    async handleMerchantSelectFocus() {
      if (!this.dataCacheStates.merchants && !this.dataLoadingStates.merchants) {
        console.log('商户选择框获得焦点，开始加载商户数据...');
        try {
          await this.loadMerchantList();
        } catch (error) {
          console.error('加载商户数据失败:', error);
          this.$message.error('加载商户数据失败，请重试');
        }
      }
    },
    async selectMerchant(item, v1) {
      console.log('选择商户:', item);

      // 参数验证
      if (!item) {
        console.error('selectMerchant: item参数为空');
        return;
      }

      // 确保商户列表已加载
      if (!this.merchantsList || this.merchantsList.length === 0) {
        console.log('商户列表为空，尝试加载...');
        try {
          await this.loadMerchantList();
        } catch (error) {
          console.error('加载商户列表失败:', error);
          this.$message.error('加载商户列表失败，请重试');
          return;
        }
      }

      // 重置所有产品状态
      this.initProductData();

      for (let i = 0; i < this.merchantsList.length; i++) {
        if (this.merchantsList[i].merchantId == item) {
          let row = this.merchantsList[i];
          let merchantInfo = {
            merchantId: row.merchantId || '',
            merchantNo: row.ybMerchantNo || ''
          };

          // 处理商户信息
          if (row.ybMerchantSubjectInfo) {
            let ybMerchantSubjectInfo = row.ybMerchantSubjectInfo;
            let merchantSubjectInfo = ybMerchantSubjectInfo
              ? JSON.parse(ybMerchantSubjectInfo)
              : {};
            merchantInfo.merchantShortName = merchantSubjectInfo.shortName;
          }

          this.currentRow = Object.assign({}, this.defRwValue, merchantInfo);

          // 处理商户产品信息
          if (row.ybProductInfo && row.ybProductInfo !== '') {
            try {
              const productInfoList = JSON.parse(row.ybProductInfo);
              console.log('商户产品信息:', productInfoList);

              // 处理VA账户产品
              this.vaProducts.forEach(product => {
                const matchedProduct = productInfoList.find(p => p.productCode === product.code);
                if (matchedProduct) {
                  this.$set(product, 'selected', true);

                  // 使用商户产品信息中的值
                  const rateType = matchedProduct.rateType || 'SINGLE_PERCENT';
                  const percentRate = matchedProduct.percentRate !== undefined ? Number(matchedProduct.percentRate) : 0.60;
                  const fixedRate = matchedProduct.fixedRate !== undefined ? Number(matchedProduct.fixedRate) : 0;

                  this.$set(product, 'rateType', rateType);
                  this.$set(product, 'percentRate', percentRate);
                  this.$set(product, 'fixedRate', fixedRate);

                  // 保存原始值
                  product._originalValues = {
                    rateType: rateType,
                    percentRate: percentRate,
                    fixedRate: fixedRate
                  };

                  // 初始化计费策略历史记录
                  if (!product._rateTypeHistory) {
                    product._rateTypeHistory = {};
                  }

                  product._rateTypeHistory['SINGLE_PERCENT'] = {
                    percentRate: percentRate,
                    fixedRate: 0
                  };

                  product._rateTypeHistory['SINGLE_FIXED'] = {
                    percentRate: 0,
                    fixedRate: fixedRate || 1
                  };

                  product._rateTypeHistory['FIXED_MIX_PERCENT'] = {
                    percentRate: percentRate,
                    fixedRate: fixedRate || 1
                  };

                  product._previousRateType = rateType;
                }
              });

              // 处理面对面产品
              this.faceProducts.forEach(product => {
                const matchedProduct = productInfoList.find(p => p.productCode === product.code);
                if (matchedProduct) {
                  this.$set(product, 'selected', true);

                  // 使用商户产品信息中的值
                  const rateType = matchedProduct.rateType || 'SINGLE_PERCENT';
                  const percentRate = matchedProduct.percentRate !== undefined ? Number(matchedProduct.percentRate) : 0.25;
                  const fixedRate = matchedProduct.fixedRate !== undefined ? Number(matchedProduct.fixedRate) : 0;

                  this.$set(product, 'rateType', rateType);
                  this.$set(product, 'percentRate', percentRate);
                  this.$set(product, 'fixedRate', fixedRate);

                  // 保存原始值
                  product._originalValues = {
                    rateType: rateType,
                    percentRate: percentRate,
                    fixedRate: fixedRate
                  };

                  // 初始化计费策略历史记录
                  if (!product._rateTypeHistory) {
                    product._rateTypeHistory = {};
                  }

                  product._rateTypeHistory['SINGLE_PERCENT'] = {
                    percentRate: percentRate,
                    fixedRate: 0
                  };

                  product._rateTypeHistory['SINGLE_FIXED'] = {
                    percentRate: 0,
                    fixedRate: fixedRate || 1
                  };

                  product._rateTypeHistory['FIXED_MIX_PERCENT'] = {
                    percentRate: percentRate,
                    fixedRate: fixedRate || 1
                  };

                  product._previousRateType = rateType;
                }
              });

              // 处理非面对面产品
              this.nonFaceProducts.forEach(product => {
                const matchedProduct = productInfoList.find(p => p.productCode === product.code);
                if (matchedProduct) {
                  this.$set(product, 'selected', true);

                  // 使用商户产品信息中的值
                  const rateType = matchedProduct.rateType || 'SINGLE_PERCENT';
                  const percentRate = matchedProduct.percentRate !== undefined ? Number(matchedProduct.percentRate) : 0.60;
                  const fixedRate = matchedProduct.fixedRate !== undefined ? Number(matchedProduct.fixedRate) : 0;

                  this.$set(product, 'rateType', rateType);
                  this.$set(product, 'percentRate', percentRate);
                  this.$set(product, 'fixedRate', fixedRate);

                  // 保存原始值
                  product._originalValues = {
                    rateType: rateType,
                    percentRate: percentRate,
                    fixedRate: fixedRate
                  };

                  // 初始化计费策略历史记录
                  if (!product._rateTypeHistory) {
                    product._rateTypeHistory = {};
                  }

                  product._rateTypeHistory['SINGLE_PERCENT'] = {
                    percentRate: percentRate,
                    fixedRate: 0
                  };

                  product._rateTypeHistory['SINGLE_FIXED'] = {
                    percentRate: 0,
                    fixedRate: fixedRate || 1
                  };

                  product._rateTypeHistory['FIXED_MIX_PERCENT'] = {
                    percentRate: percentRate,
                    fixedRate: fixedRate || 1
                  };

                  product._previousRateType = rateType;
                }
              });

              // 更新全选状态
              this.updateSelectAllStatus();

              console.log('产品加载完成:');
              console.log('VA产品:', this.vaProducts.filter(p => p.selected).map(p => ({ code: p.code, rateType: p.rateType, percentRate: p.percentRate, fixedRate: p.fixedRate })));
              console.log('面对面产品:', this.faceProducts.filter(p => p.selected).map(p => ({ code: p.code, rateType: p.rateType, percentRate: p.percentRate, fixedRate: p.fixedRate })));
              console.log('非面对面产品:', this.nonFaceProducts.filter(p => p.selected).map(p => ({ code: p.code, rateType: p.rateType, percentRate: p.percentRate, fixedRate: p.fixedRate })));

            } catch (error) {
              console.error('Failed to parse product info:', error);
            }
          } else {
            console.log('商户没有产品信息');
          }
        }
      }
    },

    async addNewEvent(forceRefresh = false) {
      // 显示弹窗
      this.showAddDetail = true;
      this.currentRow = Object.assign({}, this.defRwValue);
      this.initProductData();
      this.activeTab = "vaAccount";
      this.isReadOnly = false; // 设置为编辑模式

      // 如果需要强制刷新，清除缓存
      if (forceRefresh) {
        this.clearDataCache();
      }

      // 按需加载新增变更界面所需的数据
      await this.loadRequiredDataForAddForm();
    },

    // 初始化产品数据
    initProductData() {
      console.log('初始化产品数据');

      // 初始化VA账户产品
      this.vaProducts.forEach(product => {
        // 只修改选中状态，保留其他值
        const wasSelected = product.selected;

        // 设置选中状态为false
        this.$set(product, 'selected', false);

        // 如果之前没有设置过值，才设置默认值
        if (!product.rateType) {
          this.$set(product, 'rateType', 'SINGLE_PERCENT');
        }

        if (product.percentRate === undefined) {
          this.$set(product, 'percentRate', 0.60);
        } else {
          // 确保是数字类型
          this.$set(product, 'percentRate', Number(product.percentRate));
        }

        if (product.fixedRate === undefined) {
          this.$set(product, 'fixedRate', 1);
        } else {
          // 确保是数字类型
          this.$set(product, 'fixedRate', Number(product.fixedRate));
        }

        // 如果之前没有_originalValues，才创建
        if (!product._originalValues) {
          product._originalValues = {
            rateType: product.rateType,
            percentRate: Number(product.percentRate),
            fixedRate: Number(product.fixedRate)
          };
        }
      });

      // 初始化面对面产品
      this.faceProducts.forEach(product => {
        // 只修改选中状态，保留其他值
        const wasSelected = product.selected;

        // 设置选中状态为false
        this.$set(product, 'selected', false);

        // 如果之前没有设置过值，才设置默认值
        if (!product.rateType) {
          this.$set(product, 'rateType', 'SINGLE_PERCENT');
        }

        if (product.percentRate === undefined) {
          this.$set(product, 'percentRate', 0.25);
        } else {
          // 确保是数字类型
          this.$set(product, 'percentRate', Number(product.percentRate));
        }

        if (product.fixedRate === undefined) {
          this.$set(product, 'fixedRate', 1);
        } else {
          // 确保是数字类型
          this.$set(product, 'fixedRate', Number(product.fixedRate));
        }

        // 如果之前没有_originalValues，才创建
        if (!product._originalValues) {
          product._originalValues = {
            rateType: product.rateType,
            percentRate: Number(product.percentRate),
            fixedRate: Number(product.fixedRate)
          };
        }
      });

      // 初始化非面对面产品
      this.nonFaceProducts.forEach(product => {
        // 只修改选中状态，保留其他值
        const wasSelected = product.selected;

        // 设置选中状态为false
        this.$set(product, 'selected', false);

        // 如果之前没有设置过值，才设置默认值
        if (!product.rateType) {
          this.$set(product, 'rateType', 'SINGLE_PERCENT');
        }

        if (product.percentRate === undefined) {
          this.$set(product, 'percentRate', 0.60);
        } else {
          // 确保是数字类型
          this.$set(product, 'percentRate', Number(product.percentRate));
        }

        if (product.fixedRate === undefined) {
          this.$set(product, 'fixedRate', 1);
        } else {
          // 确保是数字类型
          this.$set(product, 'fixedRate', Number(product.fixedRate));
        }

        // 如果之前没有_originalValues，才创建
        if (!product._originalValues) {
          product._originalValues = {
            rateType: product.rateType,
            percentRate: Number(product.percentRate),
            fixedRate: Number(product.fixedRate)
          };
        }
      });

      // 重置全选状态
      this.selectAllVA = false;
      this.selectAllFace = false;
      this.selectAllNonFace = false;
    },

    // 切换产品选择状态（整个块点击）
    toggleProductSelection(product, event) {
      // 阻止事件冒泡
      event.stopPropagation();

      // 检查点击的目标元素，如果是开关或其子元素，不处理
      const target = event.target;
      if (target.closest('.el-switch') || target.classList.contains('el-switch')) {
        return;
      }

      // 切换产品选中状态
      const newSelected = !product.selected;
      this.handleProductToggle(product, newSelected);
    },

    // 处理产品开关切换
    handleProductToggle(product, selected) {
      console.log('产品开关切换:', product.code, selected);

      // 如果是关闭开关，先保存当前值
      if (!selected && product.selected) {
        // 保存当前值到_originalValues
        product._originalValues = {
          rateType: product.rateType,
          percentRate: Number(product.percentRate),
          fixedRate: Number(product.fixedRate)
        };

        // 同时保存到计费策略历史记录
        if (!product._rateTypeHistory) {
          product._rateTypeHistory = {};
        }

        product._rateTypeHistory[product.rateType] = {
          percentRate: Number(product.percentRate),
          fixedRate: Number(product.fixedRate)
        };

        // 记录当前计费策略
        product._previousRateType = product.rateType;

        console.log('保存产品值:', product._originalValues);
      }

      // 设置选中状态
      this.$set(product, 'selected', selected);

      // 如果开启产品，设置默认费率
      if (selected && !product.rateType) {
        const isFaceToFace = this.faceProducts.some(p => p.code === product.code);
        const isVAProduct = this.vaProducts.some(p => p.code === product.code);
        let defaultRate = 0.60; // 默认费率

        if (isFaceToFace) {
          defaultRate = 0.25; // 面对面产品默认费率
        } else if (isVAProduct) {
          defaultRate = 0.60; // VA账户产品默认费率
        } else {
          defaultRate = 0.60; // 非面对面产品默认费率
        }

        // 确保计费策略为百分比
        this.$set(product, 'rateType', 'SINGLE_PERCENT');
        this.$set(product, 'percentRate', defaultRate);
        this.$set(product, 'fixedRate', 0);
      }

      // 更新全选状态
      if (!selected) {
        // 如果取消选中，检查是否需要更新全选状态
        if (this.isVAProduct(product.code)) {
          this.selectAllVA = false;
        } else if (this.isFaceProduct(product.code)) {
          this.selectAllFace = false;
        } else if (this.isNonFaceProduct(product.code)) {
          this.selectAllNonFace = false;
        }
      } else {
        // 如果选中，检查是否所有产品都已选中
        if (this.isVAProduct(product.code)) {
          this.selectAllVA = this.vaProducts.every(p => p.selected);
        } else if (this.isFaceProduct(product.code)) {
          this.selectAllFace = this.faceProducts.every(p => p.selected);
        } else if (this.isNonFaceProduct(product.code)) {
          this.selectAllNonFace = this.nonFaceProducts.every(p => p.selected);
        }
      }

      // 注意：我们不再在这里修改计费策略和费率值，保留用户已设置的值
      console.log('产品开关切换后的值:', product.rateType, product.percentRate, product.fixedRate);
    },

    // 判断是否为VA账户产品
    isVAProduct(code) {
      return this.vaProducts.some(p => p.code === code);
    },

    // 判断是否为面对面产品
    isFaceProduct(code) {
      return this.faceProducts.some(p => p.code === code);
    },

    // 判断是否为非面对面产品
    isNonFaceProduct(code) {
      return this.nonFaceProducts.some(p => p.code === code);
    },

    // 全选面对面产品
    handleSelectAllFace(selected) {
      if (selected) {
        // 如果是全选，先设置默认值
        this.faceProducts.forEach(product => {
          // 设置默认计费策略和费率值
          this.$set(product, 'rateType', 'SINGLE_PERCENT');
          this.$set(product, 'percentRate', 0.25);
          this.$set(product, 'fixedRate', 0);

          // 保存到历史记录
          if (!product._rateTypeHistory) {
            product._rateTypeHistory = {};
          }

          // 为每种计费策略都保存一份初始值
          product._rateTypeHistory['SINGLE_PERCENT'] = {
            percentRate: 0.25,
            fixedRate: 0
          };

          product._rateTypeHistory['SINGLE_FIXED'] = {
            percentRate: 0,
            fixedRate: 1
          };

          product._rateTypeHistory['FIXED_MIX_PERCENT'] = {
            percentRate: 0.25,
            fixedRate: 1
          };

          // 记录当前计费策略
          product._previousRateType = 'SINGLE_PERCENT';

          // 使用handleProductToggle方法来确保计费策略正确处理
          this.handleProductToggle(product, selected);
        });
      } else {
        // 如果是取消全选，直接使用handleProductToggle方法
        this.faceProducts.forEach(product => {
          this.handleProductToggle(product, selected);
        });
      }
    },

    // 全选非面对面产品
    handleSelectAllNonFace(selected) {
      if (selected) {
        // 如果是全选，先设置默认值
        this.nonFaceProducts.forEach(product => {
          // 设置默认计费策略和费率值
          this.$set(product, 'rateType', 'SINGLE_PERCENT');
          this.$set(product, 'percentRate', 0.60);
          this.$set(product, 'fixedRate', 0);

          // 保存到历史记录
          if (!product._rateTypeHistory) {
            product._rateTypeHistory = {};
          }

          // 为每种计费策略都保存一份初始值
          product._rateTypeHistory['SINGLE_PERCENT'] = {
            percentRate: 0.60,
            fixedRate: 0
          };

          product._rateTypeHistory['SINGLE_FIXED'] = {
            percentRate: 0,
            fixedRate: 1
          };

          product._rateTypeHistory['FIXED_MIX_PERCENT'] = {
            percentRate: 0.60,
            fixedRate: 1
          };

          // 记录当前计费策略
          product._previousRateType = 'SINGLE_PERCENT';

          // 使用handleProductToggle方法来确保计费策略正确处理
          this.handleProductToggle(product, selected);
        });
      } else {
        // 如果是取消全选，直接使用handleProductToggle方法
        this.nonFaceProducts.forEach(product => {
          this.handleProductToggle(product, selected);
        });
      }
    },

    // 全选VA账户产品
    handleSelectAllVA(selected) {
      if (selected) {
        // 如果是全选，先设置默认值
        this.vaProducts.forEach(product => {
          // 设置默认计费策略和费率值
          this.$set(product, 'rateType', 'SINGLE_PERCENT');
          this.$set(product, 'percentRate', 0.60);
          this.$set(product, 'fixedRate', 1);

          // 保存到历史记录
          if (!product._rateTypeHistory) {
            product._rateTypeHistory = {};
          }

          // 为每种计费策略都保存一份初始值
          product._rateTypeHistory['SINGLE_PERCENT'] = {
            percentRate: 0.60,
            fixedRate: 0
          };

          product._rateTypeHistory['SINGLE_FIXED'] = {
            percentRate: 0,
            fixedRate: 1
          };

          product._rateTypeHistory['FIXED_MIX_PERCENT'] = {
            percentRate: 0.60,
            fixedRate: 1
          };

          // 记录当前计费策略
          product._previousRateType = 'SINGLE_PERCENT';

          // 使用handleProductToggle方法来确保计费策略正确处理
          this.handleProductToggle(product, selected);
        });
      } else {
        // 如果是取消全选，直接使用handleProductToggle方法
        this.vaProducts.forEach(product => {
          this.handleProductToggle(product, selected);
        });
      }
    },

    // 更新全选状态
    updateSelectAllStatus() {
      // 检查VA账户产品是否全部选中
      this.selectAllVA = this.vaProducts.length > 0 && this.vaProducts.every(p => p.selected);

      // 检查面对面产品是否全部选中
      this.selectAllFace = this.faceProducts.length > 0 && this.faceProducts.every(p => p.selected);

      // 检查非面对面产品是否全部选中
      this.selectAllNonFace = this.nonFaceProducts.length > 0 && this.nonFaceProducts.every(p => p.selected);
    },

    // 处理计费策略变更
    handleRateTypeChange(product) {
      console.log('计费策略变更:', product.code, product.rateType);

      // 如果没有_rateTypeHistory，创建一个
      if (!product._rateTypeHistory) {
        product._rateTypeHistory = {};
      }

      // 获取旧的计费策略类型
      const oldRateType = product._previousRateType || product.rateType;

      // 保存当前值到历史记录中，以便在切换回来时恢复
      product._rateTypeHistory[oldRateType] = {
        percentRate: Number(product.percentRate),
        fixedRate: Number(product.fixedRate)
      };

      console.log('保存历史值:', oldRateType, product._rateTypeHistory[oldRateType]);

      // 更新当前计费策略
      this.$set(product, 'rateType', product.rateType);

      // 记录当前计费策略，以便下次切换时使用
      product._previousRateType = product.rateType;

      // 根据计费策略类型调整显示的字段，优先使用历史记录中的值
      if (product.rateType === 'SINGLE_PERCENT') {
        // 固定值始终为0
        this.$set(product, 'fixedRate', 0);

        // 如果有历史记录，使用历史记录中的百分比值
        if (product._rateTypeHistory[product.rateType]) {
          this.$set(product, 'percentRate', product._rateTypeHistory[product.rateType].percentRate);
          console.log('恢复历史百分比值:', product._rateTypeHistory[product.rateType].percentRate);
        }
      } else if (product.rateType === 'SINGLE_FIXED') {
        // 百分比始终为0
        this.$set(product, 'percentRate', 0);

        // 如果有历史记录，使用历史记录中的固定值
        if (product._rateTypeHistory[product.rateType]) {
          this.$set(product, 'fixedRate', product._rateTypeHistory[product.rateType].fixedRate);
          console.log('恢复历史固定值:', product._rateTypeHistory[product.rateType].fixedRate);
        }
      } else if (product.rateType === 'FIXED_MIX_PERCENT') {
        // 如果有历史记录，使用历史记录中的值
        if (product._rateTypeHistory[product.rateType]) {
          this.$set(product, 'percentRate', product._rateTypeHistory[product.rateType].percentRate);
          this.$set(product, 'fixedRate', product._rateTypeHistory[product.rateType].fixedRate);
          console.log('恢复历史混合值:', product._rateTypeHistory[product.rateType]);
        } else {
          // 如果没有历史记录，使用默认值或保留当前值
          if (!product.percentRate && product.percentRate !== 0) {
            this.$set(product, 'percentRate', 0.60);
          }
          if (!product.fixedRate && product.fixedRate !== 0) {
            this.$set(product, 'fixedRate', 0.60);
          }
        }
      }

      // 保存当前值到_originalValues（用于开关切换）
      product._originalValues = {
        rateType: product.rateType,
        percentRate: Number(product.percentRate),
        fixedRate: Number(product.fixedRate)
      };

      console.log('计费策略变更后的值:', product.rateType, product.percentRate, product.fixedRate);
    },

    // 处理费率值变更
    handleRateValueChange(product, field, value) {
      console.log('费率值变更:', product.code, field, value);

      // 确保使用响应式更新并转换为数字类型
      this.$set(product, field, Number(value));

      // 保存当前值到_originalValues
      if (!product._originalValues) {
        product._originalValues = {
          rateType: product.rateType,
          percentRate: Number(product.percentRate),
          fixedRate: Number(product.fixedRate)
        };
      } else {
        product._originalValues[field] = Number(value);
      }

      // 同时更新计费策略历史记录
      if (!product._rateTypeHistory) {
        product._rateTypeHistory = {};
      }

      if (!product._rateTypeHistory[product.rateType]) {
        product._rateTypeHistory[product.rateType] = {
          percentRate: Number(product.percentRate),
          fixedRate: Number(product.fixedRate)
        };
      } else {
        product._rateTypeHistory[product.rateType][field] = Number(value);
      }

      console.log('更新计费策略历史记录:', product.rateType, product._rateTypeHistory[product.rateType]);
    },






    saveClickEvent() {
      // 验证是否选择了商户
      if (!this.currentRow.merchantId) {
        this.$message.warning('请选择商户');
        return;
      }

      // 验证是否选择了产品
      const hasSelectedProducts =
        this.vaProducts.some(p => p.selected) ||
        this.faceProducts.some(p => p.selected) ||
        this.nonFaceProducts.some(p => p.selected);

      if (!hasSelectedProducts) {
        this.$message.warning('请选择至少一个产品');
        return;
      }

      // 验证面对面支付产品费率
      const faceToFaceError = this.faceProducts.some(product => {
        if (product.selected) {
          if (product.rateType === 'SINGLE_PERCENT' && product.percentRate < 0.25) {
            return true;
          }
          if (product.rateType === 'FIXED_MIX_PERCENT' && product.percentRate < 0.25) {
            return true;
          }
        }
        return false;
      });
      if (faceToFaceError) {
        this.$message.warning('面对面支付产品费率百分比不能低于0.25%');
        return;
      }

      // 验证非面对面支付产品费率
      const nonFaceToFaceError = this.nonFaceProducts.some(product => {
        if (product.selected) {
          if (product.rateType === 'SINGLE_PERCENT' && product.percentRate < 0.60) {
            return true;
          }
          if (product.rateType === 'FIXED_MIX_PERCENT' && product.percentRate < 0.60) {
            return true;
          }
        }
        return false;
      });
      if (nonFaceToFaceError) {
        this.$message.warning('非面对面支付产品费率百分比不能低于0.60%');
        return;
      }

      // 构建请求数据
      const formData = {
        merchantId: this.currentRow.merchantId,
        merchantNo: this.currentRow.merchantNo,
        productInfo: []
      };

      // 收集选中的VA账户产品
      this.vaProducts.forEach(product => {
        if (product.selected) {
          const productInfo = {
            productCode: product.code,
            rateType: product.rateType,
            undertaker: "ORDINARY_MERCHANT",
            paymentMethod: "REAL_TIME"
          };

          // 根据计费策略添加相应的费率字段
          if (product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT') {
            productInfo.percentRate = Number(product.percentRate);
          }

          if (product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT') {
            productInfo.fixedRate = Number(product.fixedRate);
          }

          formData.productInfo.push(productInfo);
        }
      });

      // 收集选中的面对面产品
      this.faceProducts.forEach(product => {
        if (product.selected) {
          const productInfo = {
            productCode: product.code,
            rateType: product.rateType,
            undertaker: "ORDINARY_MERCHANT",
            paymentMethod: "REAL_TIME"
          };

          // 根据计费策略添加相应的费率字段
          if (product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT') {
            productInfo.percentRate = Number(product.percentRate);
          }

          if (product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT') {
            productInfo.fixedRate = Number(product.fixedRate);
          }

          formData.productInfo.push(productInfo);
        }
      });

      // 收集选中的非面对面产品
      this.nonFaceProducts.forEach(product => {
        if (product.selected) {
          const productInfo = {
            productCode: product.code,
            rateType: product.rateType,
            undertaker: "ORDINARY_MERCHANT",
            paymentMethod: "REAL_TIME"
          };

          // 根据计费策略添加相应的费率字段
          if (product.rateType === 'SINGLE_PERCENT' || product.rateType === 'FIXED_MIX_PERCENT') {
            productInfo.percentRate = Number(product.percentRate);
          }

          if (product.rateType === 'SINGLE_FIXED' || product.rateType === 'FIXED_MIX_PERCENT') {
            productInfo.fixedRate = Number(product.fixedRate);
          }

          formData.productInfo.push(productInfo);
        }
      });

      // 提交请求
      this.submitLoading = true;
      saveProductFeeModify(formData)
        .then(response => {
          if (response.code == 200) {
            this.showAddDetail = false;
            this.VXETable.modal.message({
              content: '操作成功',
              status: 'success'
            });
            const $grid = this.$refs.xGrid;
            $grid.commitProxy('reload');
          } else {
            this.$message.error(response.msg || '操作失败');
          }
        })
        .catch(error => {
          this.$message.error(error.message || '操作失败');
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
  },
};
</script>
<style scoped>
::v-deep .vxe-textarea--inner {
  height: 60px;
}

::v-deep .vue-treeselect__control {
  height: 32px !important;
  line-height: 32px !important;
}

.form-item {
  font-size: 15px;
  font-weight: bold;
  word-spacing: 3px;
  color: #6b6666;
  width: 100%;
  border-bottom: 1px solid #999;
  padding-bottom: 3px;
  margin-top: 10px;
}

::v-deep .cdetail .vxe-modal--box {
  max-width: 800px;
  margin: 0 auto;
}

::v-deep .cdetail .vxe-form {
  padding: 0 10px;
}

/* 产品变更相关样式 */
.product-change-container {
  padding: 0 10px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

/* 增强选项卡样式 */
.product-select-section .el-tabs__header {
  margin-bottom: 20px;
}

/* 产品选项卡样式 */
.product-tabs {
  --va-color: #67c23a;
  --payment-color: #e6a23c;
}

.product-tabs.el-tabs--card > .el-tabs__header {
  background-color: #f5f7fa;
  padding: 15px 15px 0;
  border-radius: 8px 8px 0 0;
  border: none; /* 移除边框 */
}

.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  background-color: transparent;
}

.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item {
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s;
  padding: 0 40px;
  margin-right: 4px;
  background-color: #f0f0f0;
  color: #909399;
  border-radius: 8px 8px 0 0;
  /* 移除下边框 */
  border-top: 1px solid #dcdfe6;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  border-bottom: none;
}

.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
  border-left-color: var(--va-color);
  border-top-color: var(--va-color);
  border-right-color: var(--va-color);
  border-left-width: 2px;
  border-top-width: 2px;
  border-right-width: 2px;
}

.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item:last-child {
  border-left-color: var(--payment-color);
  border-top-color: var(--payment-color);
  border-right-color: var(--payment-color);
  border-left-width: 2px;
  border-top-width: 2px;
  border-right-width: 2px;
}

.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item:hover {
  color: #606266;
  transform: translateY(-2px);
}

.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background-color: white;
  color: #303133;
  border-bottom: none;
  position: relative;
  font-size: 18px;
  z-index: 10;
  transform: translateY(-4px);
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
  /* 确保没有下边框 */
  border-bottom-color: transparent;
}

/* VA账户标签样式 */
.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item:first-child.is-active {
  color: var(--va-color);
  border-top: 3px solid var(--va-color);
}

/* 支付产品标签样式 */
.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item:last-child.is-active {
  color: var(--payment-color);
  border-top: 3px solid var(--payment-color);
}

/* 添加当前标签的指示器 */
.product-tabs.el-tabs--card > .el-tabs__header .el-tabs__item.is-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: white;
  z-index: 1;
}

/* 内容区域样式 */
.product-tabs.el-tabs--card > .el-tabs__content {
  position: relative;
  z-index: 5;
  padding: 20px;
  background-color: #fff;
  border: none; /* 移除边框 */
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* VA账户内容区域样式 - 移除顶部边框 */
.product-tabs.el-tabs--card > .el-tabs__content .el-tab-pane[label="VA账户"]::before {
  /* 移除顶部边框 */
  display: none;
}

/* 支付产品内容区域样式 - 移除顶部边框 */
.product-tabs.el-tabs--card > .el-tabs__content .el-tab-pane[label="支付产品"]::before {
  /* 移除顶部边框 */
  display: none;
}





.merchant-select-section {
  margin-bottom: 20px;
}

/* 数据加载提示样式 */
.loading-tip {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409eff;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-tip i {
  font-size: 16px;
}

/* 重试提示样式 */
.retry-tip {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.merchant-info {
  margin-top: 15px;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 5px;
}

.info-label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.product-select-section {
  margin-top: 20px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.product-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #f5f7fa;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  position: relative;
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 鼠标悬停时高亮整个产品头部区域 */
.product-item:hover .product-header {
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.product-selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.product-active {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.product-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #409eff;
  border-radius: 4px 0 0 4px;
}

/* VA账户产品特殊样式 */
.el-tab-pane[label="VA账户"] .product-item {
  border-left: 4px solid #67c23a;
}

.el-tab-pane[label="VA账户"] .product-selected {
  border-left: 4px solid #67c23a;
}

.el-tab-pane[label="VA账户"] .product-active {
  border-left: 4px solid #67c23a;
}

.el-tab-pane[label="VA账户"] .product-active::before {
  background: #67c23a;
}

/* 面对面产品特殊样式 */
.product-category:first-child .product-item {
  border-left: 4px solid #e6a23c;
}

.product-category:first-child .product-selected {
  border-left: 4px solid #e6a23c;
}

/* 非面对面产品特殊样式 */
.product-category:last-child .product-item {
  border-left: 4px solid #f56c6c;
}

.product-category:last-child .product-selected {
  border-left: 4px solid #f56c6c;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
  min-height: 32px; /* 确保有足够的高度覆盖开关 */
  padding: 4px 0; /* 增加上下内边距 */
}

.product-name {
  font-weight: bold;
  color: #303133;
  flex: 1; /* 让产品名称占据剩余空间 */
  margin-right: 10px; /* 与开关保持间距 */
}

/* 开关按钮样式优化 */
.product-header .el-switch {
  position: relative;
  z-index: 2; /* 确保开关在最上层 */
  flex-shrink: 0; /* 防止开关被压缩 */
}

/* 扩大开关的点击区域 */
.product-header .el-switch::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  z-index: -1;
}

/* 确保开关在悬停时有视觉反馈 */
.product-header .el-switch:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 产品项点击时的视觉反馈 */
.product-item:active {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}

/* 开关区域的特殊样式 */
.product-header .el-switch {
  margin: -4px; /* 扩大实际点击区域 */
  padding: 4px; /* 保持视觉大小不变 */
}

.rate-settings {
  margin-top: 15px;
  padding: 12px;
  border-top: 1px dashed #dcdfe6;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 0 0 4px 4px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.rate-type {
  margin-bottom: 15px;
}

.rate-values {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rate-value {
  display: flex;
  align-items: center;
}

.rate-label {
  width: 70px;
  text-align: right;
  margin-right: 10px;
  color: #606266;
}

.rate-unit {
  margin-left: 5px;
  color: #606266;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  background-color: #f0f7ff;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid #d1e9ff;
}

.category-title {
  font-weight: bold;
  font-size: 15px;
  color: #409eff;
  padding-left: 10px;
  border-left: 3px solid #409eff;
}

.product-category {
  margin-bottom: 30px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

/* 增强全选复选框样式 */
.category-header .el-checkbox__label {
  font-weight: bold;
  color: #606266;
}

.category-header .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #409eff;
}

/* 新增变更页面的tab样式 - 参考详情页样式 */
.product-tabs-edit {
  --va-color: #67c23a;
  --payment-color: #409eff;
}

/* 新增变更页面tab头部样式 */
.product-tabs-edit.el-tabs--card > .el-tabs__header {
  border-bottom: none;
  margin: 0 0 15px 0;
}

.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none;
}

.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s;
  padding: 0 40px;
  margin-right: 4px;
  background-color: #f0f0f0;
  color: #909399;
  border-radius: 8px 8px 0 0;
  border-top: 1px solid #dcdfe6;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  border-bottom: none;
}

.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
  border-left-color: var(--va-color);
  border-top-color: var(--va-color);
  border-right-color: var(--va-color);
  border-left-width: 2px;
  border-top-width: 2px;
  border-right-width: 2px;
}

.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item:last-child {
  border-left-color: var(--payment-color);
  border-top-color: var(--payment-color);
  border-right-color: var(--payment-color);
  border-left-width: 2px;
  border-top-width: 2px;
  border-right-width: 2px;
}

.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item:hover {
  color: #606266;
  transform: translateY(-2px);
}

.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background-color: white;
  color: #303133;
  border-bottom: none;
  position: relative;
  font-size: 18px;
  z-index: 10;
  transform: translateY(-4px);
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
  border-bottom-color: transparent;
}

/* VA账户标签样式 */
.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item:first-child.is-active {
  color: var(--va-color);
  border-top: 3px solid var(--va-color);
}

/* 支付产品标签样式 */
.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item:last-child.is-active {
  color: var(--payment-color);
  border-top: 3px solid var(--payment-color);
}

/* 添加当前标签的指示器 */
.product-tabs-edit.el-tabs--card > .el-tabs__header .el-tabs__item.is-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: white;
  z-index: 1;
}

/* 内容区域样式 */
.product-tabs-edit.el-tabs--card > .el-tabs__content {
  position: relative;
  z-index: 5;
  padding: 20px;
  background-color: #fff;
  border: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 确保产品项的点击区域覆盖所有内容 */
.product-item {
  position: relative;
  overflow: visible; /* 确保扩展的点击区域可见 */
}

/* 为产品项添加一个覆盖整个区域的点击层 */
.product-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none; /* 默认不拦截点击事件 */
}

/* 当不是开关区域时，启用点击事件拦截 */
.product-item:not(.switch-area-hover)::after {
  pointer-events: auto;
}

/* 开关区域特殊处理 */
.product-header .el-switch {
  position: relative;
  z-index: 3; /* 确保开关在覆盖层之上 */
}

/* 费率设置区域也需要在覆盖层之上 */
.rate-settings {
  position: relative;
  z-index: 2;
}

@media screen and (max-width: 768px) {
  ::v-deep .cdetail .vxe-form {
    padding: 0;
  }

  /* 移动端优化点击区域 */
  .product-header {
    min-height: 40px;
    padding: 6px 0;
  }

  .product-header .el-switch::before {
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
  }
}
</style>
